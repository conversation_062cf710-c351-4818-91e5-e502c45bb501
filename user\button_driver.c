#include "button_driver.h"

//按键判断变量
uint8_t key_val,key_old,key_down,key_up;
//按键状态列表
typedef enum{
    KEY_DEBUG = 1
}KEY_STATE;

/**
 * @brief 按键状态读取函数
 * @param none
 * @return uint8_t 
 */
KEY_STATE key_read(void)
{
    uint8_t temp = 0;
    if(DL_GPIO_readPins(KEY_PORT, KEY_PIN_21_PIN) == 0) 
    temp = KEY_DEBUG;
		
		return temp;
}
/**
 * @brief 按键任务处理函数
 * @param none
 * @return __WEAK 
 */
__WEAK void key_task(void)
{
    key_val = key_read();
    key_down = key_val & (key_old ^ key_val);
    key_up = ~key_val & (key_old ^ key_val);
    key_old = key_val;

    switch(key_down)
    {
        case KEY_DEBUG:
            my_printf(UART_0_INST,"key test suc\r\n");
        break;
    }
}