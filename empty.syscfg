/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.22.0+3893"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const I2C2    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();
const UART3   = UART.addInstance();
const UART4   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction3     = system.clockTree["HFCLKEXT"];
pinFunction3.inputFreq = 40;

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.enable       = true;
pinFunction4.inputFreq    = 40;
pinFunction4.HFXTStartup  = 100;
pinFunction4.HFCLKMonitor = true;

GPIO1.$name                              = "LED1";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins[0].$name            = "PIN_22";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].assignedPin      = "22";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "KEY";
GPIO2.port                               = "PORTB";
GPIO2.associatedPins[0].$name            = "PIN_21";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPin      = "21";

GPIO3.$name                              = "ENCODER";
GPIO3.port                               = "PORTB";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name            = "left_a";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO3.associatedPins[0].interruptEn      = true;
GPIO3.associatedPins[0].polarity         = "RISE";
GPIO3.associatedPins[0].pin.$assign      = "PB6";
GPIO3.associatedPins[1].$name            = "left_b";
GPIO3.associatedPins[1].direction        = "INPUT";
GPIO3.associatedPins[1].internalResistor = "PULL_UP";
GPIO3.associatedPins[1].interruptEn      = true;
GPIO3.associatedPins[1].polarity         = "FALL";
GPIO3.associatedPins[1].pin.$assign      = "PB7";
GPIO3.associatedPins[2].$name            = "right_a";
GPIO3.associatedPins[2].direction        = "INPUT";
GPIO3.associatedPins[2].interruptEn      = true;
GPIO3.associatedPins[2].polarity         = "RISE";
GPIO3.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO3.associatedPins[2].pin.$assign      = "PB8";
GPIO3.associatedPins[3].$name            = "right_b";
GPIO3.associatedPins[3].direction        = "INPUT";
GPIO3.associatedPins[3].internalResistor = "PULL_UP";
GPIO3.associatedPins[3].interruptEn      = true;
GPIO3.associatedPins[3].polarity         = "FALL";
GPIO3.associatedPins[3].pin.$assign      = "PB9";

GPIO4.$name                         = "MOTOR_DIR_LEFT1";
GPIO4.associatedPins[0].$name       = "PIN_0";
GPIO4.associatedPins[0].pin.$assign = "PA22";

GPIO5.$name                         = "MOTOR_DIR_LEFT2";
GPIO5.associatedPins[0].$name       = "PIN_1";
GPIO5.associatedPins[0].pin.$assign = "PB24";

GPIO6.$name                         = "MOTOR_DIR_RIGHT1";
GPIO6.associatedPins[0].$name       = "PIN_2";
GPIO6.associatedPins[0].pin.$assign = "PA24";

GPIO7.$name                         = "MOTOR_DIR_RIGHT2";
GPIO7.associatedPins[0].$name       = "PIN_3";
GPIO7.associatedPins[0].pin.$assign = "PA26";

GPIO8.$name                         = "BNO_RST";
GPIO8.associatedPins[0].$name       = "PIN_4";
GPIO8.associatedPins[0].pin.$assign = "PB14";

I2C1.$name                             = "IMU";
I2C1.basicEnableController             = true;
I2C1.peripheral.$assign                = "I2C1";
I2C1.peripheral.sdaPin.$assign         = "PB3";
I2C1.peripheral.sclPin.$assign         = "PB2";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
I2C1.sdaPinConfig.enableConfig         = true;
I2C1.sdaPinConfig.internalResistor     = "PULL_UP";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
I2C1.sclPinConfig.enableConfig         = true;
I2C1.sclPinConfig.internalResistor     = "PULL_UP";

I2C2.$name                             = "GRAY";
I2C2.basicEnableController             = true;
I2C2.peripheral.$assign                = "I2C0";
I2C2.peripheral.sdaPin.$assign         = "PA0";
I2C2.peripheral.sclPin.$assign         = "PA1";
I2C2.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric11";
I2C2.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric12";

PWM1.timerStartTimer                    = true;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.ccIndex                            = [1];
PWM1.$name                              = "MOTOR_PWM_LEFT";
PWM1.clockPrescale                      = 2;
PWM1.peripheral.$assign                 = "TIMG8";
PWM1.peripheral.ccp1Pin.$assign         = "PB11";
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC0";

PWM2.$name                              = "MOTOR_PWM_RIGHT";
PWM2.timerStartTimer                    = true;
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.clockDivider                       = 2;
PWM2.clockPrescale                      = 2;
PWM2.ccIndex                            = [1];
PWM2.peripheral.$assign                 = "TIMG7";
PWM2.peripheral.ccp1Pin.$assign         = "PA27";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric13";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";

SYSCTL.clockTreeEn           = true;
SYSCTL.forceDefaultClkConfig = true;
SYSCTL.validateClkStatus     = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 80000;
SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "0";
SYSTICK.systickEnable     = true;

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                    = "UART_1";
UART2.uartClkSrc               = "MFCLK";
UART2.targetBaudRate           = 115200;
UART2.enabledInterrupts        = ["RX"];
UART2.peripheral.$assign       = "UART1";
UART2.peripheral.rxPin.$assign = "PA9";
UART2.peripheral.txPin.$assign = "PA8";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";

UART3.$name                    = "UART_2";
UART3.targetBaudRate           = 115200;
UART3.enabledInterrupts        = ["RX"];
UART3.uartClkSrc               = "MFCLK";
UART3.peripheral.$assign       = "UART2";
UART3.peripheral.rxPin.$assign = "PB16";
UART3.peripheral.txPin.$assign = "PB15";
UART3.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric7";
UART3.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric8";

UART4.$name                    = "UART_3";
UART4.uartClkSrc               = "MFCLK";
UART4.targetBaudRate           = 115200;
UART4.enabledInterrupts        = ["RX"];
UART4.peripheral.$assign       = "UART3";
UART4.peripheral.rxPin.$assign = "PB13";
UART4.peripheral.txPin.$assign = "PB12";
UART4.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric9";
UART4.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric10";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB22";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PB21";
