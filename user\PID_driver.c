#include "PID_driver.h"

int basic_speed =60; // 基础速度

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_angle;

PID_T pid_line;


/* PID 参数定义 */
PidParams_t pid_params_left = {
    .kp = 12.0f,        
    .ki = 1.2000f,      
    .kd = 30.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_right = {
    .kp = 12.0f,        
    .ki = 1.2000f,      
    .kd = 30.00f,   
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_angle = {
    .kp = 1.2f,        
    .ki = 0.0001f,      
    .kd = 10.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_line = {
    .kp = 6.5f,        
    .ki = 0.3000f,      
    .kd = 0.25f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

void PID_Init(void)
{
	pid_init(&pid_speed_left,
			pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
			0.0f, pid_params_left.out_max);
  
	pid_init(&pid_speed_right,
			pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
			0.0f, pid_params_right.out_max);
	pid_init(&pid_angle,
			pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
			0.0f, pid_params_angle.out_max);
	pid_init(&pid_line,
			pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
			0.0f, pid_params_line.out_max);

	pid_set_target(&pid_speed_left, basic_speed);
	pid_set_target(&pid_speed_right, basic_speed);
//	pid_set_target(&pid_angle, 0);
//	pid_set_target(&pid_line, 0);
}

//void PID_Angle_Control(void)
//{
//	float output_yaw=0.0f;
//	
//	output_yaw=pid_calculate_positional(&pid_angle, Yaw);
//	
//	output_yaw=pid_constrain(output_yaw, pid_params_angle.out_min, pid_params_angle.out_max);
//	
//	pid_set_target(&pid_speed_left, basic_speed-output_yaw);
//	pid_set_target(&pid_speed_right, basic_speed+output_yaw);
//	
////	Uart_Printf(&huart1, "{angledata}%d,%.2f\r\n", 0, output_yaw);
//	
//}

//void PID_Line_Control(void)
//{
//	float output_line=0.0f;
//	
//	output_line=pid_calculate_positional(&pid_line, g_line_position_error);
//	
//	output_line=pid_constrain(output_line, pid_params_line.out_min, pid_params_line.out_max);
//	
//	pid_set_target(&pid_speed_left, basic_speed-output_line);
//	pid_set_target(&pid_speed_right, basic_speed+output_line);
//	
////	Uart_Printf(&huart1, "{pidline}%f,%f\r\n", pid_line.target, g_line_position_error);
//	
////	Uart_Printf(&huart1, "{linedata}%d,%.2f\r\n", 0, output_line);
//}

// 低通滤波器系数 (Low-pass filter coefficient 'alpha')
// alpha 越小, 滤波效果越强, 但延迟越大。建议从 0.1 到 0.5 之间开始尝试。
#define SPEED_FILTER_ALPHA_LEFT  0.15f 
#define SPEED_FILTER_ALPHA_RIGHT 0.15f 

// 用于存储滤波后速度的变量
static float filtered_speed_left = 0.0f;
static float filtered_speed_right = 0.0f;

bool pid_running = false; // PID 控制使能开关

unsigned char PID_mode=0;

void PID_Task(void)
{
	if(pid_running == false) return;
  
	float output_left, output_right;
 
	// filtered = alpha * raw + (1 - alpha) * previous_filtered
	filtered_speed_left = SPEED_FILTER_ALPHA_LEFT * encoder_left.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_LEFT) * filtered_speed_left;
	filtered_speed_right = SPEED_FILTER_ALPHA_RIGHT * encoder_right.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_RIGHT) * filtered_speed_right;
	
	output_left = pid_calculate_incremental(&pid_speed_left, filtered_speed_left);
	output_right = pid_calculate_incremental(&pid_speed_right, filtered_speed_right);
		//output_yaw =  pid_calculate_incremental(&pid_angle, yaw); //-> PID计算 YAW -> 反馈
		//pid_set_target(&pid_speed_left,v + output_yaw); -> 作用对象
		//pid_set_target(&pid_speed_right,v - output_yaw);
	output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
	output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
	// 设置电机速度
	my_printf(UART_0_INST,"{LEFT}%.2f,%.2f\r\n", basic_speed, output_left);
	Motor_SetSpeed(&left_motor, output_left);
	Motor_SetSpeed(&right_motor, output_right);
	
	
	
}

